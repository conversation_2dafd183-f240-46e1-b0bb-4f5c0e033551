# Documentation Application Mobile VitaBrosse

## 📱 Vue d'ensemble

**VitaBrosse** est une application de gestion commerciale complète développée avec React, TypeScript et Firebase. Elle permet la gestion des équipes commerciales, des clients, des produits, des commandes et des missions de merchandising.

## 🚀 Technologies utilisées

- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM v7
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Package Manager**: npm

## 🏗️ Architecture de l'application

### Structure des dossiers
```
src/
├── components/          # Composants réutilisables
│   ├── layout/         # Composants de mise en page
│   ├── modals/         # Modales pour CRUD operations
│   └── features/       # Composants spécifiques aux fonctionnalités
├── contexts/           # Contextes React (Auth, App)
├── firebase/           # Configuration et services Firebase
├── hooks/              # Hooks personnalisés
├── models/             # Types TypeScript et modèles de données
├── pages/              # Pages principales de l'application
└── utils/              # Utilitaires et helpers
```

## 🔐 Authentification et Sécurité

### Système d'authentification
- **Firebase Authentication** pour la gestion des utilisateurs
- **Rôles utilisateurs**: admin, commercial, merchandizer
- **Protection des routes** avec ProtectedRoute component
- **Gestion des sessions** avec persistance locale

### Fonctionnalités d'authentification
- Connexion avec email/mot de passe
- Inscription de nouveaux utilisateurs
- Réinitialisation de mot de passe
- Profils utilisateurs stockés dans Firestore
- Déconnexion sécurisée

## 📊 Modules principaux

### 1. Dashboard (Tableau de bord)
- **Vue d'ensemble** des métriques clés
- **Statistiques en temps réel**: ventes, clients actifs, commandes
- **Top clients** avec pourcentages de contribution
- **Commandes récentes** avec statuts
- **Graphiques de croissance** pour tous les indicateurs

### 2. Gestion des Commerciaux
- **CRUD complet** pour les commerciaux
- **Comptes utilisateurs** pour l'application mobile
- **Territoires et départements** assignés
- **Permissions et rôles** configurables
- **Statuts de compte**: actif, inactif, suspendu, en attente

#### Fonctionnalités avancées des comptes commerciaux:
- Mot de passe temporaire pour première connexion
- Obligation de changer le mot de passe
- Accès hors ligne configurable
- Authentification biométrique
- Timeout de session personnalisable
- Permissions granulaires (édition prix, remises max)

### 3. Gestion des Clients
- **Base de données clients** complète
- **Informations de contact** et entreprise
- **Statuts clients**: actif, inactif
- **Historique des interactions**
- **Adresses et coordonnées**

### 4. Gestion des Produits
- **Catalogue produits** avec catégories
- **Prix et descriptions** détaillées
- **Statuts de disponibilité**
- **Images et spécifications**
- **Gestion des stocks**

### 5. Gestion des Merchandiseurs
- **Équipe merchandising** avec profils complets
- **Compétences et spécialisations**
- **Disponibilités et plannings**
- **Historique des missions**

### 6. Gestion des Catalogues
- **Catalogues produits** organisés
- **Versions et mises à jour**
- **Distribution aux équipes**
- **Suivi des consultations**

### 7. Gestion des Commandes
- **Suivi complet des commandes**
- **Statuts**: en attente, confirmée, expédiée, livrée, annulée
- **Calculs automatiques** (sous-totaux, taxes, totaux)
- **Historique des modifications**
- **Notifications de statut**

### 8. Gestion des Missions
- **Planification des missions** merchandising
- **Attribution aux merchandiseurs**
- **Suivi en temps réel** des statuts
- **Géolocalisation** des points de vente
- **Rapports de mission** avec photos
- **Calendrier intégré** pour la planification

## 🔧 Configuration Firebase

### Services utilisés
- **Firestore**: Base de données NoSQL pour toutes les données
- **Authentication**: Gestion des utilisateurs et sessions
- **Storage**: Stockage des fichiers et images

### Collections Firestore
- `users`: Profils utilisateurs et authentification
- `commercials`: Données des commerciaux
- `clients`: Base de données clients
- `products`: Catalogue produits
- `orders`: Commandes et transactions
- `merchandizers`: Équipe merchandising
- `catalogues`: Catalogues produits
- `missions`: Missions et tâches

## 📱 Fonctionnalités mobiles

### Interface utilisateur
- **Design responsive** adapté mobile et desktop
- **Navigation intuitive** avec sidebar
- **Modales optimisées** pour les écrans tactiles
- **Indicateurs de statut** visuels
- **Notifications en temps réel**

### Performance
- **Cache local** avec Firestore persistence
- **Chargement optimisé** des données
- **Gestion hors ligne** partielle
- **Synchronisation automatique**

## 🛠️ Installation et déploiement

### Prérequis
- Node.js 18+
- npm ou yarn
- Compte Firebase configuré

### Installation
```bash
npm install
```

### Développement
```bash
npm run dev
```

### Build de production
```bash
npm run build
```

### Déploiement
```bash
npm run preview
```

## 🔍 Fonctionnalités avancées

### Gestion des états
- **Context API** pour l'état global
- **Hooks personnalisés** pour la logique métier
- **Gestion des erreurs** centralisée
- **Loading states** pour UX optimale

### Sécurité
- **Validation des données** côté client et serveur
- **Règles Firestore** pour la sécurité
- **Sanitisation des inputs**
- **Protection CSRF**

### Monitoring
- **Logs détaillés** pour le debugging
- **Métriques de performance**
- **Suivi des erreurs**
- **Analytics d'utilisation**

## 📞 Support et maintenance

### Logs et debugging
- Console logs détaillés en développement
- Gestion d'erreurs avec try/catch
- Messages d'erreur utilisateur friendly
- Monitoring Firebase intégré

### Mises à jour
- Versioning sémantique
- Migrations de données automatisées
- Tests de régression
- Déploiement progressif

## 📋 Guide d'utilisation pour les utilisateurs finaux

### Connexion à l'application
1. **Accès**: Ouvrir l'application VitaBrosse
2. **Authentification**: Saisir email et mot de passe
3. **Première connexion**: Changer le mot de passe temporaire si requis
4. **Navigation**: Accéder au tableau de bord principal

### Navigation principale
- **Sidebar gauche**: Menu principal avec toutes les sections
- **Indicateurs visuels**: Section active mise en évidence
- **Responsive**: Adaptation automatique mobile/desktop
- **Déconnexion**: Bouton en haut à droite

### Gestion des données
- **Ajout**: Bouton "+" dans chaque section
- **Modification**: Clic sur un élément pour éditer
- **Suppression**: Bouton de suppression avec confirmation
- **Recherche**: Barre de recherche dans les listes
- **Filtres**: Options de tri et filtrage avancées

## 🔧 Configuration technique

### Variables d'environnement
```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

### Scripts disponibles
```json
{
  "dev": "vite",                    // Serveur de développement
  "build": "vite build",            // Build de production
  "lint": "eslint .",               // Vérification du code
  "preview": "vite preview"         // Aperçu du build
}
```

### Dépendances principales
- **react**: ^18.3.1 - Framework principal
- **firebase**: ^11.10.0 - Backend et authentification
- **react-router-dom**: ^7.7.0 - Routing
- **lucide-react**: ^0.344.0 - Icônes
- **tailwindcss**: ^3.4.1 - Styling

## 🐛 Résolution des problèmes courants

### Problèmes de connexion
- **Erreur Firebase**: Vérifier la configuration Firebase
- **Timeout**: Problème de réseau, réessayer
- **Mot de passe**: Utiliser la réinitialisation
- **Cache**: Vider le cache du navigateur

### Problèmes de performance
- **Chargement lent**: Vérifier la connexion internet
- **Données manquantes**: Actualiser la page
- **Synchronisation**: Vérifier le statut réseau
- **Mémoire**: Fermer les onglets inutiles

### Erreurs fréquentes
- **Permission denied**: Vérifier les droits utilisateur
- **Network error**: Problème de connectivité
- **Invalid data**: Vérifier les champs obligatoires
- **Session expired**: Se reconnecter

## 📊 Modèles de données

### Structure Commercial
```typescript
interface Commercial {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  employeeId: string;
  territory: string;
  account: {
    username: string;
    accountStatus: 'active' | 'inactive' | 'suspended';
    permissions: string[];
    canAccessOffline: boolean;
    maxDiscountPercentage: number;
  };
}
```

### Structure Client
```typescript
interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  address: string;
  status: 'active' | 'inactive';
}
```

### Structure Mission
```typescript
interface Mission {
  id: string;
  merchandizerId: string;
  clientId: string;
  title: string;
  description: string;
  missionDate: Timestamp;
  status: 'pending' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
}
```

## 🔒 Sécurité et permissions

### Niveaux d'accès
- **Admin**: Accès complet à toutes les fonctionnalités
- **Commercial**: Gestion clients et commandes assignés
- **Merchandizer**: Accès missions et rapports

### Règles de sécurité Firestore
```javascript
// Exemple de règles pour la collection commercials
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /commercials/{commercialId} {
      allow read, write: if request.auth != null
        && request.auth.token.role == 'admin';
      allow read: if request.auth != null
        && request.auth.uid == commercialId;
    }
  }
}
```

### Bonnes pratiques
- **Mots de passe forts**: Minimum 8 caractères
- **Sessions**: Timeout automatique après inactivité
- **Données sensibles**: Chiffrement côté client
- **Logs**: Audit trail des actions importantes

---

**Version**: 1.0.0
**Dernière mise à jour**: 2025-01-17
**Développé par**: Équipe VitaBrosse
**Support**: <EMAIL>
